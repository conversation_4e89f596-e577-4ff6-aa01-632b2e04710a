<?php

namespace App\Livewire\Notes;

use App\Models\Note;
use Flux\Flux;
use GuzzleHttp\Psr7\Request;
use Livewire\Component;

class Create extends Component
{
    public $title;
    public $content;

    protected function rules()
    {
        return [
            'title' => 'required',
            'content' => 'required',
        ];
    }

    protected function messages()
    {
        return [
            'title.required' => 'Title tidak boleh kosong',
            'content.required' => 'Content tidak boleh kosong',
        ];
    }

    public function render()
    {
        return view('livewire.notes.create');
    }



    public function save()
    {
        $validatedData = $this->validate();

        Note::create($validatedData);

        $this->reset(['title', 'content']);

        Flux::modal('create-notes')->close();

        session()->flash('success', 'Notes created successfully');

        $this->redirectRoute('notes', navigate: true);
    }
}
