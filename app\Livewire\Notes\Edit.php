<?php

namespace App\Livewire\Notes;

use App\Models\Note;
use Flux\Flux;
use Livewire\Component;
use Livewire\Attributes\On;

class Edit extends Component
{
    public $title;
    public $content;
    public $note_id;

    protected function rules()
    {
        return [
            'title' => 'required',
            'content' => 'required',
        ];
    }

    protected function messages()
    {
        return [
            'title.required' => 'Title tidak boleh kosong',
            'content.required' => 'Content tidak boleh kosong',
        ];
    }

    public function resetForm()
    {
        $this->reset(['title', 'content']);
        $this->resetValidation();
    }

    public function render()
    {
        return view('livewire.notes.edit');
    }

    #[On('edit-note')]
    public function editNote($id)
    {
        // dd("dispatch accepted : {$id}");

        $notes = Note::where('id', $id)->first();

        $this->note_id = $notes->id;
        $this->title = $notes->title;
        $this->content = $notes->content;

        Flux::modal('edit-notes')->show();
    }

    public function save()
    {
        $this->validate();

        Note::find($this->note_id)->update([
            'title' => $this->title,
            'content' => $this->content,
        ]);


        Flux::modal('edit-notes')->close();

        $this->resetForm();

        session()->flash('success', 'Notes updated successfully');

        $this->redirectRoute('notes', navigate: true);
    }
}
