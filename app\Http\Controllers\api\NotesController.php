<?php

namespace App\Http\Controllers\api;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Resources\NoteResource;
use App\Models\Note;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;


class NotesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return response()->json([
            'status' => true,
            'message' => 'Selamat Datang di Api Notes'
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validate = Validator::make($request->all(), [
            'title' => 'required',
            'content' => 'required'
        ], [
            'title.required' => 'Judul Tidak Boleh Kosong',
            'content.required' => 'Konten Tidak Boleh Kosong'
        ]);

        if ($validate->fails()) {
            return response()->json([
                'status' => false,
                'message' => 'Data Tidak Lengkap',
                'data' => $validate->errors()
            ], 422);
        } else {
            Note::create($request->all());
            return response()->json([
                'status' => true,
                'message' => 'Data Berhasil Disimpan',
                'data' => $request->all()
            ], 201);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $note = Note::find($id);
        if ($note) {
            return response()->json([
                'status' => true,
                'message' => 'Data Berhasil Ditemukan',
                'data' => NoteResource($note)
            ], 200);
        } else {
            return response()->json([
                'status' => false,
                'message' => 'Data Tidak Ditemukan',
                'data' => null
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
