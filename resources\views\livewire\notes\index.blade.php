<div class="relative mb-6 w-full">
    <flux:heading size="xl" level="1">{{ __('List Notes') }}</flux:heading>
    <flux:subheading size="lg" class="mb-6">{{ __('Create and manage Notes') }}</flux:subheading>
    <flux:separator variant="subtle" />

    <div class="py-2">

        <x-messages.alert-message />

        <!-- Form Modal Create and update -->

        <livewire:notes.edit />
        <livewire:notes.create />

        <div class="flex justify-between items-center gap-4">
            <flux:modal.trigger name="create-notes">
                <flux:button variant="primary" size="sm" color="green" icon="plus">Create</flux:button>
            </flux:modal.trigger>
            <div class="w-80">
                <flux:input icon="magnifying-glass" size="sm" wire:model.live="search" placeholder="Search..." />
            </div>
        </div>


        <!-- table -->
        <table class="table-auto w-full table-sm shadow-md rounded-md mt-5">
            <thead class='bg-slate-700 text-white'>
                <tr>
                    <th class="px-2 py-3 text-sm" style="width: 50px;">No</th>
                    <th class="px-3 py-3 text-left text-sm">Title</th>
                    <th class="px-3 py-3 text-left text-sm">Content</th>
                    <th class="px-3 py-3 text-left text-sm">Created At</th>
                    <th class="px-3 py-3 text-left text-sm">Updated Time</th>
                    <th class="px-3 py-3 text-center text-sm" style="width: 200px;">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($notes as $note)
                <tr class="border-b hover:bg-teal-400">
                    <td class="px-1 py-2 text-sm">{{ $notes->firstItem() + $loop->index }}</td>
                    <td class="px-1 py-2 text-sm">{{ $note->title }}</td>
                    <td class="px-1 py-2 text-sm">{{ $note->content }}</td>
                    <td class="px-1 py-2 text-sm">{{ $note->created_at->format('d M Y') }}</td>
                    <td class="px-1 py-2 text-sm">{{ $note->updated_at->format('h:m:s') }}</td>
                    <td class="px-1 py-2 text-sm">
                        <flux:button variant="ghost" color="yellow" size="xs" wire:click="edit({{ $note->id }})">
                            <flux:icon name="pencil" size="xs" />
                        </flux:button>
                        <flux:button variant="ghost" color="red" size="xs" wire:click="delete({{ $note->id }})">
                            <flux:icon variant="solid" color="red" name="trash" size="xs" />
                        </flux:button>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
        {{ $notes->links() }}
    </div>

    <!-- Modal delete -->
    <flux:modal wire:model="id" name="delete-note" class="min-w-[22rem]">
        <div class="space-y-6">
            <div>
                <flux:heading size="lg">Apakah anda yakin ingin menghapus Id notes {{ $this->note_id }} ini ?</flux:heading>

                <flux:text class="mt-2">
                    <p>You're about to delete this notes.</p>
                    <p>This action cannot be reversed.</p>
                </flux:text>
            </div>

            <div class="flex gap-2">
                <flux:spacer />

                <flux:modal.close>
                    <flux:button variant="ghost">Cancel</flux:button>
                </flux:modal.close>

                <flux:button wire:click="deleteNotes" type="submit" variant="danger">Delete</flux:button>
            </div>
        </div>
    </flux:modal>

</div>