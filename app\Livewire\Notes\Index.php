<?php

namespace App\Livewire\Notes;

use Livewire\Component;
use App\Models\Note;
use Flux\Flux;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public $title;
    public $content;
    public $note_id;
    public $search;

    public function render()

    {
        $notes = Note::where('title', 'like', '%' . $this->search . '%')
            ->orWhere('content', 'like', '%' . $this->search . '%')
            ->orderBy('created_at', 'desc')->paginate(5);

        return view('livewire.notes.index', compact('notes'));
    }

    public function edit($id)
    {

        $this->dispatch('edit-note', $id);
    }

    public function delete($id)
    {
        $this->note_id = $id;
        Flux::modal('delete-note')->show();
    }


    public function deleteNotes()
    {
        Note::find($this->note_id)->delete();
        Flux::modal('delete-note')->close();

        session()->flash('success', 'Note deleted successfully!');

        $this->redirectRoute('notes', navigate: true);
    }
}
