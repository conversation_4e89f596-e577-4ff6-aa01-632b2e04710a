@session('success')
<div
    x-data="{ show: true }"
    x-show="show" x-init="setTimeout(() => show = false, 3000)"
    class="bg-green-400 text-white p-4 rounded-lg shadow-md z-50 fixed top-5 right-10 text-sm" role="alert">
    <p class="font-bold">Success Message</p>
    <p>{{ session('success') }}</p>
</div>
@endsession

@session('error')
<div
    x-data="{ show: true }"
    x-show="show" x-init="setTimeout(() => show = false, 3000)"
    class="bg-red-400 text-white p-4 rounded-lg shadow-md z-50 fixed top-5 right-10 text-sm" role="alert">
    <p class="font-bold">Error Message</p>
    <p>{{ session('error') }}</p>
</div>
@endsession