@import "tailwindcss";
@import "../../vendor/livewire/flux/dist/flux.css";

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../vendor/livewire/flux-pro/stubs/**/*.blade.php';
@source '../../vendor/livewire/flux/stubs/**/*.blade.php';

@custom-variant dark (&:where(.dark, .dark *));

@theme {
    --font-sans: "Instrument Sans", ui-sans-serif, system-ui, sans-serif,
        "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol",
        "Noto Color Emoji";

    --color-zinc-50: var(--color-gray-50);
    --color-zinc-100: var(--color-gray-100);
    --color-zinc-200: var(--color-gray-200);
    --color-zinc-300: var(--color-gray-300);
    --color-zinc-400: var(--color-gray-400);
    --color-zinc-500: var(--color-gray-500);
    --color-zinc-600: var(--color-gray-600);
    --color-zinc-700: var(--color-gray-700);
    --color-zinc-800: var(--color-gray-800);
    --color-zinc-900: var(--color-gray-900);
    --color-zinc-950: var(--color-gray-950);

    --color-accent: var(--color-violet-500);
    --color-accent-content: var(--color-violet-600);
    --color-accent-foreground: var(--color-white);
}

@layer theme {
    .dark {
        --color-accent: var(--color-violet-500);
        --color-accent-content: var(--color-violet-400);
        --color-accent-foreground: var(--color-white);
    }
}

@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

[data-flux-field]:not(ui-radio, ui-checkbox) {
    @apply grid gap-2;
}

[data-flux-label] {
    @apply !mb-0 !leading-tight;
}

input:focus[data-flux-control],
textarea:focus[data-flux-control],
select:focus[data-flux-control] {
    @apply outline-hidden ring-2 ring-accent ring-offset-2 ring-offset-accent-foreground;
}

/* \[:where(&)\]:size-4 {
    @apply size-4;
} */
